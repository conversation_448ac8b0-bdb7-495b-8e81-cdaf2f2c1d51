#!/bin/bash

echo "========================================"
echo "    SIMDOC Environment Setup Script"
echo "========================================"
echo

# Check if we're in the right directory
if [ ! -f "environment.dart.example" ]; then
    echo "❌ ERROR: environment.dart.example not found!"
    echo "Please run this script from lib/config directory"
    echo
    exit 1
fi

# Check if environment.dart already exists
if [ -f "environment.dart" ]; then
    echo "⚠️  WARNING: environment.dart already exists!"
    echo
    read -p "Do you want to overwrite it? (y/N): " overwrite
    if [[ ! $overwrite =~ ^[Yy]$ ]]; then
        echo "Setup cancelled."
        exit 0
    fi
fi

# Copy the template
echo "Copying environment.dart.example to environment.dart..."
cp environment.dart.example environment.dart

if [ $? -eq 0 ]; then
    echo "✅ File copied successfully!"
    echo
    echo "⚠️  NEXT STEPS:"
    echo "1. Open environment.dart in your editor"
    echo "2. Replace placeholder values with your Firebase config"
    echo "3. Get Firebase config from: https://console.firebase.google.com"
    echo "4. Save the file"
    echo "5. Run: flutter clean && flutter pub get"
    echo
    echo "📖 For detailed instructions, read README.md"
    echo
else
    echo "❌ Failed to copy file!"
    echo "Please copy manually: cp environment.dart.example environment.dart"
    echo
fi

# Make the script executable
chmod +x setup-environment.sh

echo "Press any key to continue..."
read -n 1
