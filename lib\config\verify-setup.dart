// lib/config/verify-setup.dart
// Script untuk memverifikasi konfigurasi environment
import 'dart:io';

void main() {
  print('========================================');
  print('    SIMDOC Environment Verification');
  print('========================================\n');

  // Check if environment.dart exists
  final envFile = File('environment.dart');
  if (!envFile.existsSync()) {
    print('❌ ERROR: environment.dart not found!');
    print('Please run setup script first:');
    print('  Windows: lib/config/setup-environment.bat');
    print('  Linux/Mac: lib/config/setup-environment.sh');
    exit(1);
  }

  print('✅ environment.dart found');

  // Read and check content
  final content = envFile.readAsStringSync();

  // Check for placeholder values
  final checks = [
    {'key': 'your-firebase-project-id', 'name': 'Project ID'},
    {'key': 'your-project.firebasestorage.app', 'name': 'Storage Bucket'},
    {'key': 'your-firebase-api-key', 'name': 'API Key'},
    {'key': 'your-firebase-app-id', 'name': 'App ID'},
    {'key': 'your-messaging-sender-id', 'name': 'Messaging Sender ID'},
  ];

  bool hasPlaceholders = false;

  for (final check in checks) {
    if (content.contains(check['key']!)) {
      print('⚠️  ${check['name']} still contains placeholder value');
      hasPlaceholders = true;
    } else {
      print('✅ ${check['name']} configured');
    }
  }

  print('\n========================================');

  if (hasPlaceholders) {
    print('❌ SETUP INCOMPLETE');
    print('\nPlease update environment.dart with your actual Firebase values:');
    print('1. Open Firebase Console: https://console.firebase.google.com');
    print('2. Select your project');
    print('3. Go to Project Settings');
    print('4. Copy the configuration values');
    print('5. Replace placeholder values in environment.dart');
  } else {
    print('✅ SETUP COMPLETE');
    print('\nYour environment is properly configured!');
    print('You can now run: flutter clean && flutter pub get');
  }

  print('========================================');
}
