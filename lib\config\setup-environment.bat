@echo off
echo ========================================
echo    SIMDOC Environment Setup Script
echo ========================================
echo.

REM Check if we're in the right directory
if not exist "environment.dart.example" (
    echo ERROR: environment.dart.example not found!
    echo Please run this script from lib/config directory
    echo.
    pause
    exit /b 1
)

REM Check if environment.dart already exists
if exist "environment.dart" (
    echo WARNING: environment.dart already exists!
    echo.
    set /p overwrite="Do you want to overwrite it? (y/N): "
    if /i not "%overwrite%"=="y" (
        echo Setup cancelled.
        pause
        exit /b 0
    )
)

REM Copy the template
echo Copying environment.dart.example to environment.dart...
copy environment.dart.example environment.dart >nul

if %errorlevel% equ 0 (
    echo ✅ File copied successfully!
    echo.
    echo ⚠️  NEXT STEPS:
    echo 1. Open environment.dart in your editor
    echo 2. Replace placeholder values with your Firebase config
    echo 3. Get Firebase config from: https://console.firebase.google.com
    echo 4. Save the file
    echo 5. Run: flutter clean && flutter pub get
    echo.
    echo 📖 For detailed instructions, read README.md
    echo.
) else (
    echo ❌ Failed to copy file!
    echo Please copy manually: copy environment.dart.example environment.dart
    echo.
)

pause
