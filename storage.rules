// storage.rules
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Helper function
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isAdmin() {
      return request.auth.token.role == 'admin';
    }
    
    // Documents storage
    match /documents/{allPaths=**} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && 
        request.resource.size < 50 * 1024 * 1024; // 50MB limit
      allow update, delete: if isAdmin();
    }
    
    // Profile images
    match /profile-images/{userId}/{allPaths=**} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && request.auth.uid == userId;
    }
    
    // Temporary uploads
    match /temp/{userId}/{allPaths=**} {
      allow read, write: if isAuthenticated() && request.auth.uid == userId;
    }
  }
}