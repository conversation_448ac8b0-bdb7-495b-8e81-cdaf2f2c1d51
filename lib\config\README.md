# Environment Configuration Setup

## 📋 Langkah-langkah Setup Environment.dart

### 1. **Persiapan <PERSON>**
```bash
# Pastikan Anda berada di root project
cd simdoc-bapeltan-flutter

# Masuk ke direktori config
cd lib/config
```

### 2. **Copy File Template**

#### Cara Otomatis (Direkomendasikan):
```bash
# Windows
setup-environment.bat

# Linux/Mac
chmod +x setup-environment.sh
./setup-environment.sh
```

#### Cara Manual:
```bash
# Windows (Command Prompt)
copy environment.dart.example environment.dart

# Windows (PowerShell)
Copy-Item environment.dart.example environment.dart

# Linux/Mac
cp environment.dart.example environment.dart
```

### 3. **Buka Firebase Console**
1. Buka [Firebase Console](https://console.firebase.google.com)
2. Pilih project **"simdoc-bapeltan"**
3. Klik ⚙️ **Settings** > **Project settings**
4. Scroll ke bawah ke bagian **"Your apps"**
5. Pilih aplikasi Android/iOS yang sudah ada

### 4. **Ambil Konfigurasi Firebase**

#### Untuk Android:
1. Klik aplikasi Android
2. Klik **"Download google-services.json"**
3. Buka file tersebut, cari nilai-nilai berikut:
   ```json
   {
     "project_info": {
       "project_id": "simdoc-bapeltan"  // ← Copy ini
     },
     "client": [
       {
         "client_info": {
           "mobilesdk_app_id": "1:783908721269:android:..."  // ← Copy ini
         },
         "api_key": [
           {
             "current_key": "AIzaSy..."  // ← Copy ini
           }
         ]
       }
     ]
   }
   ```

#### Untuk Web:
1. Klik **"Add app"** > **Web** (jika belum ada)
2. Copy konfigurasi yang muncul:
   ```javascript
   const firebaseConfig = {
     apiKey: "AIzaSy...",           // ← Copy ini
     authDomain: "...",
     projectId: "simdoc-bapeltan", // ← Copy ini
     storageBucket: "...",         // ← Copy ini
     messagingSenderId: "...",     // ← Copy ini
     appId: "..."                  // ← Copy ini
   };
   ```

### 5. **Edit File environment.dart**
Buka file `environment.dart` dan ganti nilai-nilai berikut:

```dart
class Environment {
  // Ganti dengan nilai dari Firebase Console
  static const String projectId = 'simdoc-bapeltan';
  static const String storageBucket = 'simdoc-bapeltan.firebasestorage.app';
  static const String apiKey = 'AIzaSy...';  // Dari Firebase Console
  static const String appId = '1:783908721269:android:...';  // Dari Firebase Console
  static const String messagingSenderId = '783908721269';  // Dari Firebase Console

  // Pengaturan development (biasanya tidak perlu diubah)
  static const bool useEmulator = false;
  static const String emulatorHost = 'localhost';
  static const int firestorePort = 8080;
  static const int authPort = 9099;
  static const int storagePort = 9199;
}
```

### 6. **Verifikasi Setup**

#### Verifikasi Otomatis:
```bash
# Dari root project
dart run lib/config/verify-setup.dart
```

#### Verifikasi Manual:
```bash
# Kembali ke root project
cd ../..

# Test build aplikasi
flutter clean
flutter pub get
flutter build apk --debug
```

### 7. **Troubleshooting**

#### Jika mendapat error "File not found":
```bash
# Pastikan file ada
dir lib\config\environment.dart  # Windows
ls -la lib/config/environment.dart  # Linux/Mac

# Jika tidak ada, copy ulang dari template
cd lib/config
copy environment.dart.example environment.dart  # Windows
cp environment.dart.example environment.dart    # Linux/Mac
```

#### Jika mendapat error Firebase:
1. Pastikan semua nilai sudah benar
2. Cek apakah project Firebase aktif
3. Pastikan API sudah di-enable di Firebase Console
4. Restart aplikasi setelah mengubah konfigurasi

## 🔒 Security Notes

### ⚠️ PENTING - Jangan Commit File Ini!
```bash
# File ini sudah di-exclude di .gitignore
# JANGAN hapus baris ini dari .gitignore:
**/lib/config/environment.dart
```

### ✅ Yang Boleh di-Commit:
- `environment.dart.example` ✅
- `README.md` ✅
- File konfigurasi lainnya ✅

### ❌ Yang TIDAK Boleh di-Commit:
- `environment.dart` ❌
- File yang berisi API keys ❌
- `google-services.json` ❌

## 🚀 Untuk Tim Development

### Setup untuk Developer Baru:
1. Clone repository
2. Ikuti langkah 1-6 di atas
3. Minta konfigurasi Firebase dari team lead
4. Test aplikasi berjalan dengan benar

### Multiple Environment:
```bash
# Development
environment.dart

# Production
environment.prod.dart

# Staging
environment.staging.dart
```

## 📞 Bantuan

Jika mengalami kesulitan:
1. Pastikan mengikuti semua langkah berurutan
2. Cek Firebase Console untuk konfigurasi yang benar
3. Tanya team lead untuk akses Firebase project
4. Pastikan internet connection stabil saat setup
