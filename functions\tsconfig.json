{"compilerOptions": {"module": "commonjs", "noImplicitReturns": true, "noUnusedLocals": false, "outDir": "lib", "sourceMap": true, "strict": true, "target": "es2017", "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node"}, "compileOnSave": true, "include": ["src"], "exclude": ["node_modules", "lib"]}