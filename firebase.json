{"functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run build"]}, {"source": "simdoc-bapeltan", "codebase": "simdoc-bapeltan", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint", "npm --prefix \"$RESOURCE_DIR\" run build"]}], "firestore": {"database": "(default)", "location": "asia-southeast2", "rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "storage": {"rules": "storage.rules"}, "emulators": {"auth": {"port": 9099}, "functions": {"port": 5001}, "firestore": {"port": 8080}, "storage": {"port": 9199}, "ui": {"enabled": true}, "singleProjectMode": true}, "hosting": {"public": "public", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"]}}