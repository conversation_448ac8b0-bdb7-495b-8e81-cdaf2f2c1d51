# 📱 SIMDOC BAPELTAN

**Sistem Manajemen Dokumen Digital untuk BAPELTAN**

[![Flutter](https://img.shields.io/badge/Flutter-3.16.0-blue.svg)](https://flutter.dev/)
[![Firebase](https://img.shields.io/badge/Firebase-Latest-orange.svg)](https://firebase.google.com/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)

## 🎯 Overview

SIMDOC BAPELTAN adalah aplikasi mobile untuk manajemen dokumen digital yang dibangun khusus untuk Badan Pela<PERSON>yuluhan <PERSON>tanian, <PERSON><PERSON><PERSON> dan <PERSON> (BAPELTAN). Aplikasi ini menyediakan sistem yang aman dan efisien untuk mengelola, menyimpan, dan berbagi dokumen dengan kontrol akses berbasis peran.

## ✨ Fitur Utama

### 🔐 Sistem Autentikasi
- **Registrasi Mandiri**: Pengguna dapat mendaftar dengan email dan password
- **Login Aman**: Autentikasi menggunakan Firebase Auth
- **Manajemen Sesi**: Session management dengan remember me
- **Reset Password**: Fitur lupa password (coming soon)

### 👥 Manajemen Pengguna
- **Role-based Access Control**: Admin dan User dengan permission berbeda
- **User Management**: Admin dapat mengelola pengguna
- **Permission System**: Sistem izin granular untuk kontrol akses
- **Profile Management**: Pengguna dapat mengedit profil sendiri

### 📁 Manajemen Dokumen
- **Upload Multi-file**: Upload beberapa file sekaligus dengan progress tracking
- **View All Files**: Pengguna dapat melihat semua file di sistem
- **Download All Files**: Pengguna dapat mengunduh semua file (bukan hanya milik sendiri)
- **Organize Files**: Pengguna dapat mengatur semua file ke dalam kategori
- **File Preview**: Preview dokumen tanpa perlu download
- **Search & Filter**: Pencarian dan filter dokumen yang powerful

### 🗂️ Manajemen Kategori
- **Category Organization**: Organisasi dokumen dalam kategori
- **Category Management**: Admin dapat mengelola kategori
- **Bulk Operations**: Operasi massal pada dokumen

### 📊 Analytics & Monitoring
- **Activity Logs**: Log aktivitas pengguna untuk audit
- **Storage Usage**: Monitoring penggunaan storage
- **User Analytics**: Statistik penggunaan sistem (Admin only)

## 🏗️ Arsitektur Sistem

### Frontend (Flutter)
- **State Management**: Provider pattern
- **UI Framework**: Material Design 3
- **Responsive Design**: Support mobile, tablet, dan desktop
- **Offline Support**: Caching untuk performa optimal

### Backend (Firebase)
- **Authentication**: Firebase Auth
- **Database**: Cloud Firestore
- **Storage**: Firebase Cloud Storage
- **Functions**: Cloud Functions untuk business logic
- **Hosting**: Firebase Hosting (optional)

### Permission System
```
USER Role:
✅ Upload files
✅ View ALL users' files
✅ Download ALL users' files
✅ Organize ALL users' files
❌ Delete files
❌ Update files
❌ User management

ADMIN Role:
✅ All USER permissions
✅ Delete any file
✅ Update any file
✅ User management
✅ System configuration
✅ View all activities
```

## 🚀 Quick Start

### Prerequisites
- Flutter SDK 3.16.0+
- Dart SDK 3.2.0+
- Android Studio / VS Code
- Firebase CLI
- Node.js 18+ (untuk Cloud Functions)

### Installation

1. **Clone Repository**
   ```bash
   git clone https://github.com/rahmathanifpurnama/simdoc-bapeltan-flutter.git
   cd simdoc-bapeltan-flutter
   ```

2. **Install Dependencies**
   ```bash
   flutter pub get
   cd functions && npm install && cd ..
   ```

3. **Firebase Setup**
   ```bash
   # Login ke Firebase
   firebase login
   
   # Setup Firebase project
   firebase use your-project-id
   
   # Deploy Cloud Functions
   firebase deploy --only functions
   
   # Deploy Firestore rules
   firebase deploy --only firestore:rules
   
   # Deploy Storage rules
   firebase deploy --only storage
   ```

4. **Configure Firebase**
   - Download `google-services.json` dari Firebase Console
   - Place di `android/app/google-services.json`
   - Download `GoogleService-Info.plist` untuk iOS (jika diperlukan)

5. **Run Application**
   ```bash
   flutter run
   ```

## 📱 Screenshots

| Login Screen | Home Screen | Document List | Upload Screen |
|--------------|-------------|---------------|---------------|
| ![Login](screenshots/login.png) | ![Home](screenshots/home.png) | ![Documents](screenshots/documents.png) | ![Upload](screenshots/upload.png) |

## 🛠️ Development

### Project Structure
```
lib/
├── core/                   # Core utilities dan constants
│   ├── constants/         # App constants (colors, strings, routes)
│   ├── services/          # Core services (Firebase, HTTP)
│   └── utils/             # Utility functions
├── models/                # Data models
├── providers/             # State management (Provider)
├── screens/               # UI screens
│   ├── auth/             # Authentication screens
│   ├── common/           # Common screens
│   ├── admin/            # Admin-only screens
│   └── user/             # User screens
├── services/              # Business logic services
├── widgets/               # Reusable widgets
│   ├── common/           # Common widgets
│   └── custom/           # Custom widgets
└── main.dart             # App entry point

functions/                 # Cloud Functions
├── src/
│   ├── modules/          # Function modules
│   └── index.ts          # Functions entry point
└── package.json
```

### Code Style
- Follow [Dart Style Guide](https://dart.dev/guides/language/effective-dart/style)
- Use meaningful variable and function names
- Add comments for complex logic
- Follow Flutter best practices

### Testing
```bash
# Run unit tests
flutter test

# Run integration tests
flutter test integration_test/

# Run with coverage
flutter test --coverage
```

## 🔧 Configuration

### Environment Variables
Create `lib/config/environment.dart`:
```dart
class Environment {
  static const String projectId = 'your-firebase-project-id';
  static const String storageBucket = 'your-storage-bucket';
  static const bool useEmulator = false; // Set true for development
}
```

### Firebase Configuration
- **Authentication**: Email/Password enabled
- **Firestore**: Production mode with security rules
- **Storage**: Production mode with security rules
- **Functions**: Deployed with proper permissions

## 📚 Documentation

- [Setup Guide](docs/SETUP.md) - Panduan setup lengkap
- [API Documentation](docs/API.md) - Dokumentasi Cloud Functions
- [User Guide](docs/USER_GUIDE.md) - Panduan pengguna
- [Admin Guide](docs/ADMIN_GUIDE.md) - Panduan administrator
- [Deployment Guide](docs/DEPLOYMENT.md) - Panduan deployment

## 🤝 Contributing

1. Fork repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

### Development Guidelines
- Write tests for new features
- Update documentation
- Follow code style guidelines
- Test on multiple devices/screen sizes

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👥 Team

- **Developer**: Rahmat Hanif Purnama
- **Organization**: BAPELTAN
- **Contact**: [<EMAIL>](mailto:<EMAIL>)

## 🙏 Acknowledgments

- Flutter team untuk framework yang amazing
- Firebase team untuk backend services
- Material Design team untuk design system
- Open source community untuk packages yang digunakan

## 📞 Support

Jika Anda mengalami masalah atau memiliki pertanyaan:

1. Check [Issues](https://github.com/rahmathanifpurnama/simdoc-bapeltan-flutter/issues) untuk masalah yang sudah ada
2. Buat [New Issue](https://github.com/rahmathanifpurnama/simdoc-bapeltan-flutter/issues/new) jika masalah belum ada
3. Contact developer melalui email

## 🔄 Changelog

### Version 1.0.0 (Latest)
- ✅ Initial release
- ✅ User authentication system
- ✅ Document management with role-based permissions
- ✅ File upload/download functionality
- ✅ Category management
- ✅ User management (Admin)
- ✅ Activity logging
- ✅ Responsive UI design

### Upcoming Features
- 🔄 Email verification
- 🔄 Push notifications
- 🔄 Advanced search filters
- 🔄 Document versioning
- 🔄 Bulk operations
- 🔄 Export/import functionality

---

**Made with ❤️ for BAPELTAN**